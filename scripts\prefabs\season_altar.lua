local assets = {}
local prefabs = {}

local function UpdateAltarIcon(inst)
    if inst.MiniMapEntity == nil then return end
    if inst._cooldown and inst._cooldown > GetTime() then
        -- 冷却中：提高优先级并切换为更暖色的原版图标以示“冷却中”
        inst.MiniMapEntity:SetPriority(1)
        inst.MiniMapEntity:SetIcon("firepit.png")
    else
        inst.MiniMapEntity:SetPriority(0)
        inst.MiniMapEntity:SetIcon("moonbase.png")
    end
end

local function TryActivate(inst, doer)
    if inst._cooldown and inst._cooldown > GetTime() then
        if doer and doer.components.talker then
            doer.components.talker:Say("祭坛正在冷却...")
        end
        return
    end
    if inst._cores and inst._cores >= 4 then
        -- 召唤Boss占位：生成季冠树守
        if inst.SoundEmitter then inst.SoundEmitter:PlaySound("dontstarve/common/teleportworm/travel") end
        local x,y,z = inst.Transform:GetWorldPosition()
        local boss = SpawnPrefab("boss_season_warden")
        if boss then boss.Transform:SetPosition(x+2, 0, z+2) end
        inst._cores = 0
        inst._cooldown = GetTime() + (TUNING.SEASON_BOSS_COOLDOWN_DAYS or 5) * (TUNING.TOTAL_DAY_TIME or 480)
        UpdateAltarIcon(inst)
    else
        if doer and doer.components.talker then
            doer.components.talker:Say("需要4枚季芯。")
        end
    end
end

local function OnAccept(inst, giver, item)
    if item.prefab == "season_core" then
        inst._cores = (inst._cores or 0) + 1
        if inst._cores >= 4 then
            inst:DoTaskInTime(0.2, function() TryActivate(inst, giver) end)
        end
        return true
    end
    return false
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    -- 小地图图标（复用 moonbase 图标颜色变化）
    if inst.components.minimap == nil then
        inst.entity:AddMiniMapEntity()
        inst.MiniMapEntity:SetIcon("moonbase.png")
    end

    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    inst.AnimState:SetBank("moonbase")
    inst.AnimState:SetBuild("moonbase")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("structure")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst._cores = 0

    inst:AddComponent("inspectable")

    inst:AddComponent("trader")
    inst.components.trader:SetAcceptTest(function(inst, item)
        return item.prefab == "season_core"
    end)

    -- 冷却提示：右上角消息（服务器端），每5秒一次，冷却时才提示
    inst:DoPeriodicTask(5, function()
        if inst._cooldown and inst._cooldown > GetTime() and TheNet:GetIsServer() then
            local remain = math.max(0, inst._cooldown - GetTime())
            local days = math.ceil(remain / (TUNING.TOTAL_DAY_TIME or 480))
            for _, p in ipairs(AllPlayers or {}) do
                if p and p:IsValid() and p.components and p.components.talker then
                    p.components.talker:Say(string.format("季祭坛冷却中（%d天）", days))
                end
            end
        end
    end)
    inst.components.trader.onaccept = OnAccept

    inst:AddComponent("lootdropper")

    return inst
end

return Prefab("season_altar", fn, assets, prefabs)
