local assets = {}
local prefabs = {}

local function ApplySeasonStats(inst, owner)
    if not owner or not owner:IsValid() then return end
    local season = TheWorld and TheWorld.state and TheWorld.state.season or "autumn"

    -- 默认清空，再按季节设置
    if inst.components.waterproofer then
        inst.components.waterproofer:SetEffectiveness(0)
    end
    if inst.components.insulator then
        inst.components.insulator:SetInsulation(0)
    end

    if season == "spring" then
        if inst.components.waterproofer then
            inst.components.waterproofer:SetEffectiveness(0.4)
        end
        if inst.components.insulator then
            inst.components.insulator:SetInsulation(0)
        end
    elseif season == "summer" then
        if inst.components.insulator then
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_HEAT)
            inst.components.insulator:SetSummer()
        end
    elseif season == "autumn" then
        -- 饥饿-5%：用装备时调整所有者饥饿速率
        if owner.components and owner.components.hunger then
            if inst._old_rate == nil then
                inst._old_rate = owner.components.hunger:GetRate()
            end
            owner.components.hunger:SetRate(inst._old_rate * 0.95)
        end
    elseif season == "winter" then
        if inst.components.insulator then
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_COLD)
            inst.components.insulator:SetWinter()
        end
    end
end

local function ClearOwnerAdjust(inst, owner)
    if owner and owner.components and owner.components.hunger and inst._old_rate then
        owner.components.hunger:SetRate(inst._old_rate)
    end
    inst._old_rate = nil
end

local function onequip(inst, owner)
    owner.AnimState:OverrideSymbol("swap_body", "armor_sanity", "swap_body") -- 临时可见占位
    ApplySeasonStats(inst, owner)
    if inst._seasonwatch ~= nil then inst._seasonwatch:Cancel() end
    inst._seasonwatch = owner:WatchWorldState("season", function()
        ApplySeasonStats(inst, owner)
    end)
end

local function onunequip(inst, owner)
    owner.AnimState:ClearOverrideSymbol("swap_body")
    if inst._seasonwatch ~= nil then
        inst._seasonwatch:Cancel()
        inst._seasonwatch = nil
    end
    ClearOwnerAdjust(inst, owner)
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("armor_sanity")
    inst.AnimState:SetBuild("armor_sanity")
    inst.AnimState:PlayAnimation("idle")

    inst.entity:SetPristine()

    inst:AddTag("waterproofer")

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")

    inst:AddComponent("equippable")
    inst.components.equippable.equipslot = GLOBAL.EQUIPSLOTS.BODY
    inst.components.equippable:SetOnEquip(onequip)
    inst.components.equippable:SetOnUnequip(onunequip)

    inst:AddComponent("waterproofer")
    inst.components.waterproofer:SetEffectiveness(0)

    inst:AddComponent("insulator")
    inst.components.insulator:SetInsulation(0)

    return inst
end

return Prefab("climate_cloak", fn, assets, prefabs)
