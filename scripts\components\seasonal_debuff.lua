local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local net_float = _G.net_float
local net_ushort = _G.net_ushort

local SeasonalDebuff = Class(function(self, inst)
    self.inst = inst
    self.slow_mult = 1.0
    self.slow_duration = 0
    self.slow_task = nil
    
    -- 网络同步
    if not TheWorld.ismastersim then
        self._slow_mult = net_float(inst.GUID, "seasonal_debuff.slow_mult", "slow_mult_dirty")
        self._slow_duration = net_ushort(inst.GUID, "seasonal_debuff.slow_duration", "slow_duration_dirty")
        
        inst:ListenForEvent("slow_mult_dirty", function()
            self.slow_mult = self._slow_mult:value()
            self:UpdateVisuals()
        end)
        
        inst:ListenForEvent("slow_duration_dirty", function()
            self.slow_duration = self._slow_duration:value()
        end)
        return
    end
    
    -- 服务端初始化网络变量
    self._slow_mult = net_float(inst.GUI<PERSON>, "seasonal_debuff.slow_mult", "slow_mult_dirty")
    self._slow_duration = net_ushort(inst.GUID, "seasonal_debuff.slow_duration", "slow_duration_dirty")
end)

function SeasonalDebuff:ApplySlow(multiplier, duration, source_is_boss)
    if not TheWorld.ismastersim then return end
    
    -- Boss减免效果
    if self.inst:HasTag("epic") and source_is_boss then
        multiplier = math.max(0.85, multiplier + 0.05) -- -20%变为-15%
        duration = duration * 0.75 -- 2秒变为1.5秒
    end
    
    self.slow_mult = multiplier
    self.slow_duration = duration
    
    -- 同步到客户端
    self._slow_mult:set(multiplier)
    self._slow_duration:set(duration)
    
    -- 应用移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:SetExternalSpeedMultiplier(self.inst, "seasonal_slow", multiplier)
    end
    
    -- 清理旧任务
    if self.slow_task then
        self.slow_task:Cancel()
    end
    
    -- 设置持续时间
    self.slow_task = self.inst:DoTaskInTime(duration, function()
        self:ClearSlow()
    end)
    
    -- 视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:ClearSlow()
    if not TheWorld.ismastersim then return end
    
    self.slow_mult = 1.0
    self.slow_duration = 0
    
    -- 同步到客户端
    self._slow_mult:set(1.0)
    self._slow_duration:set(0)
    
    -- 移除移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:RemoveExternalSpeedMultiplier(self.inst, "seasonal_slow")
    end
    
    -- 清理任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end
    
    -- 清理视觉效果
    self:ClearVisuals()
end

function SeasonalDebuff:UpdateVisuals()
    -- 简单的颜色提示：减速时略微偏蓝
    if self.slow_mult < 1.0 and self.inst.AnimState then
        local intensity = (1.0 - self.slow_mult) * 0.3 -- 最大30%蓝色调
        self.inst.AnimState:SetAddColour(0, 0, intensity, 0)
    end
end

function SeasonalDebuff:ClearVisuals()
    if self.inst.AnimState then
        self.inst.AnimState:SetAddColour(0, 0, 0, 0)
    end
end

function SeasonalDebuff:GetSlowMultiplier()
    return self.slow_mult
end

function SeasonalDebuff:IsSlowed()
    return self.slow_mult < 1.0
end

return SeasonalDebuff
