local assets = {}
local prefabs = {}

local function DoSlow(target, mult, dur)
    if target and target.components and target.components.locomotor then
        local tag = "season_blade_slow"
        target.components.locomotor:SetExternalSpeedMultiplier(target, tag, mult)
        target:DoTaskInTime(dur, function()
            if target.components and target.components.locomotor then
                target.components.locomotor:RemoveExternalSpeedMultiplier(target, tag)
            end
        end)
    end
end

local function DoBurnDot(attacker, target, dps, dur)
    if target and target.components and target.components.health and not target.components.health:IsDead() then
        local ticks = math.floor(dur / 0.5)
        for i = 1, ticks do
            target:DoTaskInTime(0.5 * i, function()
                if target.components and target.components.health and not target.components.health:IsDead() then
                    target.components.health:DoDelta(-dps * 0.5, nil, attacker and attacker.prefab or "season_blade")
                end
            end)
        end
    end
end

local function BurstFX(attacker, pos, color)
    -- 占位FX：复用staffcastfx，并根据配置强度调整音量/震动
    local strength = (GetModConfigData("vfx_strength") or 1)
    if attacker and attacker.SoundEmitter then
        local vol = strength == 0 and 0.3 or (strength == 2 and 1.0 or 0.6)
        attacker.SoundEmitter:PlaySound("dontstarve/common/staff_spell", nil, vol)
    end
    if attacker and attacker:IsValid() then
        local fx = SpawnPrefab("staff_castinglight")
        if fx ~= nil then
            fx.Transform:SetPosition(pos[1], pos[2], pos[3])
            fx:DoTaskInTime(0.4, fx.Remove)
        end
        if strength > 0 and attacker.components and attacker.components.playercontroller then
            -- 简易相机震动：客户端上由Cameras找API更合适；这里先占位
            -- 可用 TheFocalPoint.components.shakeaura/Camera:Shake 在完整环境中实现
        end
    end
end

local function DoBurst(attacker, target)
    if not attacker or not target then return end
    local season = TheWorld.state.season or "autumn"
    local x, y, z = target.Transform:GetWorldPosition()
    local ents
    if season == "spring" then
        ents = TheSim:FindEntities(x, y, z, 3, {"_combat"}, {"player", "companion", "FX"})
        for _, v in ipairs(ents) do
            if v ~= target and v.components and v.components.combat then
                v.components.combat:GetAttacked(attacker, TUNING.SEASON_BLADE_BURST_DMG_SPRING)
            end
        end
        if target.components and target.components.combat then
            local dmg = TUNING.SEASON_BLADE_BURST_DMG_SPRING * 0.5
            target.components.combat:GetAttacked(attacker, dmg)
        end
    elseif season == "summer" then
        ents = TheSim:FindEntities(x, y, z, 3, {"_combat"}, {"player", "companion", "FX"})
        for _, v in ipairs(ents) do
            if v.components and v.components.combat then
                v.components.combat:GetAttacked(attacker, TUNING.SEASON_BLADE_BURST_DMG_SUMMER)
                DoBurnDot(attacker, v, 1, 3)
            end
        end
    elseif season == "autumn" then
        if attacker.components and attacker.components.hunger then
            attacker.components.hunger:DoDelta(2)
        end
        if attacker.components and attacker.components.sanity then
            attacker.components.sanity:DoDelta(3)
        end
        if target.components and target.components.combat then
            target.components.combat:GetAttacked(attacker, TUNING.SEASON_BLADE_BURST_DMG_AUTUMN)
        end
    elseif season == "winter" then
        ents = TheSim:FindEntities(x, y, z, 2, {"_combat"}, {"player", "companion", "FX"})
        for _, v in ipairs(ents) do
            if v.components and v.components.combat then
                v.components.combat:GetAttacked(attacker, TUNING.SEASON_BLADE_BURST_DMG_WINTER)
                DoSlow(v, 0.8, 2)
            end
        end
        DoSlow(target, 0.9, 1.5)
    end
    BurstFX(attacker, {x, y, z})
end

local function OnAttack(inst, attacker, target)
    if not attacker or not target then return end
    if not TheWorld.ismastersim then return end

    -- 基础季节效果（每次命中都会尝试应用）
    local season = TheWorld.state.season or "autumn"
    local isboss = target:HasTag("epic")
    if season == "spring" then
        local extra = 0
        if target.GetIsWet ~= nil and target:GetIsWet() then
            extra = (TUNING.SEASON_BLADE_DAMAGE or 34) * 0.15
        end
        if extra > 0 and target.components and target.components.combat then
            target.components.combat:GetAttacked(attacker, extra)
        end
    elseif season == "summer" then
        if not isboss then
            DoBurnDot(attacker, target, 1, 2)
        else
            -- Boss仅伤害
            if target.components and target.components.combat then
                target.components.combat:GetAttacked(attacker, 2)
            end
        end
    elseif season == "autumn" then
        if inst.components and inst.components.finiteuses and math.random() < 0.05 then
            inst.components.finiteuses:Use(-1) -- 返还1点耐久
        end
    elseif season == "winter" then
        -- 使用新的seasonal_debuff组件
        if not target.components.seasonal_debuff then
            target:AddComponent("seasonal_debuff")
        end
        local mult = isboss and 0.95 or 0.9
        local dur = isboss and 1.5 or 2
        target.components.seasonal_debuff:ApplySlow(mult, dur, false)
    end

    -- 叠层爆发逻辑
    attacker._season_blade = attacker._season_blade or {
        target = nil,
        stacks = 0,
        lasttime = 0,
        cd = 0,
    }
    local data = attacker._season_blade
    local t = GetTime()
    local same = data.target ~= nil and data.target == target
    if not same or (t - data.lasttime) > TUNING.SEASON_BLADE_STACK_WINDOW then
        data.target = target
        data.stacks = 0
    end
    data.stacks = math.min(TUNING.SEASON_BLADE_STACKS_MAX, (data.stacks or 0) + 1)
    data.lasttime = t

    if data.stacks >= TUNING.SEASON_BLADE_STACKS_MAX and t >= (data.cd or 0) then
        -- 触发爆发
        DoBurst(attacker, target)
        data.stacks = 0
        data.cd = t + TUNING.SEASON_BLADE_BURST_CD
        -- 若目标是Boss，发送一次“武器爆发”事件用于破盾计数
        if target:HasTag("season_warden") then
            target:PushEvent("season_sigil", { source = "weapon_burst" })
        end
    end
end

local function onequip(inst, owner)
    owner.AnimState:OverrideSymbol("swap_object", "swap_spear", "swap_spear")
    owner.AnimState:Show("ARM_carry")
    owner.AnimState:Hide("ARM_normal")
end

local function onunequip(inst, owner)
    owner.AnimState:Hide("ARM_carry")
    owner.AnimState:Show("ARM_normal")
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("spear")
    inst.AnimState:SetBuild("spear")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("sharp")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")

    inst:AddComponent("inventoryitem")

    inst:AddComponent("weapon")
    inst.components.weapon:SetDamage(TUNING.SEASON_BLADE_DAMAGE)
    inst.components.weapon:SetOnAttack(OnAttack)

    inst:AddComponent("finiteuses")
    inst.components.finiteuses:SetMaxUses(TUNING.SEASON_BLADE_USES)
    inst.components.finiteuses:SetUses(TUNING.SEASON_BLADE_USES)
    inst.components.finiteuses:SetOnFinished(inst.Remove)

    inst:AddComponent("equippable")
    inst.components.equippable:SetOnEquip(onequip)
    inst.components.equippable:SetOnUnequip(onunequip)

    return inst
end

return Prefab("season_blade", fn, assets, prefabs)
